# Server Configuration
PORT=5000
NODE_ENV=development
SERVER_BASE=http://localhost:5000
CLIENT_URL=http://localhost:3000

# AWS S3 Configuration
AWS_ACCESS_KEY=your_aws_access_key_here
AWS_SECRET_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
S3_BUCKET=your-business-cards-bucket

# Sharing Configuration
SHARE_BASE_URL=https://cards.yourdomain.com

# Apple Wallet Configuration (Optional)
APPLE_PASS_TYPE_ID=pass.com.yourcompany.businesscard
APPLE_TEAM_ID=YOUR_APPLE_TEAM_ID

# Google Pay Configuration (Optional)
GOOGLE_PAY_ISSUER_EMAIL=<EMAIL>
GOOGLE_PAY_ISSUER_ID=your-google-pay-issuer-id

# Security (Optional - for production)
JWT_SECRET=your_jwt_secret_for_signing_tokens
ENCRYPTION_KEY=your_32_character_encryption_key
