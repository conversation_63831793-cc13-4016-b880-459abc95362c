import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import dotenv from "dotenv";

dotenv.config();

const s3 = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_KEY
  }
});

export async function uploadToS3(buffer, key, contentType) {
  try {
    const params = {
      Bucket: process.env.S3_BUCKET,
      Key: key,
      Body: buffer,
      ACL: "public-read",
      ContentType: contentType,
      CacheControl: "max-age=31536000", // 1 year cache
    };
    
    const command = new PutObjectCommand(params);
    await s3.send(command);
    
    return `https://${process.env.S3_BUCKET}.s3.amazonaws.com/${key}`;
  } catch (error) {
    console.error("S3 upload error:", error);
    throw new Error(`Failed to upload to S3: ${error.message}`);
  }
}

export function getS3Url(key) {
  return `https://${process.env.S3_BUCKET}.s3.amazonaws.com/${key}`;
}
