import express from "express";
import cors from "cors";
import path from "path";
import { fileURLToPath } from "url";
import cardRouter from "./routes/card.js";
import { securityHeaders } from "./middleware/security.js";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

const app = express();

// Security middleware
app.use(securityHeaders);
app.use(cors({
  origin: process.env.CLIENT_URL || "http://localhost:3000",
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static file serving
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

app.use("/api", cardRouter);

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server on ${PORT}`));