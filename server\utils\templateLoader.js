import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const templatesDir = path.join(__dirname, '../../templates');

// Load all template files
export function loadTemplates() {
  try {
    const templateFiles = fs.readdirSync(templatesDir)
      .filter(file => file.endsWith('.json'))
      .sort();

    const templates = templateFiles.map(file => {
      const filePath = path.join(templatesDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      return {
        id: path.basename(file, '.json'),
        ...JSON.parse(content)
      };
    });

    return templates;
  } catch (error) {
    console.error('Error loading templates:', error);
    return [];
  }
}

// Get a specific template by ID
export function getTemplate(templateId) {
  const templates = loadTemplates();
  return templates.find(t => t.id === templateId) || templates[0]; // fallback to first template
}

// Get template by index (for hash-based selection)
export function getTemplateByIndex(index) {
  const templates = loadTemplates();
  return templates[index % templates.length];
}

// Apply template styles to HTML generation
export function applyTemplate(template, data, style) {
  const { colors, fonts } = style;
  const { layout, styles } = template;

  // Generate CSS based on template configuration
  const css = `
    .card {
      width: ${styles.cardWidth}px;
      height: ${styles.cardHeight}px;
    }
    
    .left {
      width: ${layout.leftWidth};
      padding: ${styles.padding.left}px ${styles.padding.left}px;
    }
    
    .right {
      width: ${layout.rightWidth};
      padding: ${styles.padding.right}px ${styles.padding.right}px ${styles.padding.bottom}px ${styles.padding.right}px;
    }
    
    .photo-container {
      width: ${styles.photo.width}px;
      height: ${styles.photo.height}px;
      border-radius: ${styles.photo.borderRadius};
      border: ${styles.photo.border};
    }
    
    .logo {
      width: ${styles.logo.width}px;
      height: ${styles.logo.height}px;
      ${styles.logo.position === 'absolute' ? `
        position: absolute;
        top: ${styles.logo.top}px;
        right: ${styles.logo.right}px;
      ` : ''}
    }
    
    h1 {
      font-size: ${styles.typography.name.fontSize}px;
      font-weight: ${styles.typography.name.fontWeight};
      line-height: ${styles.typography.name.lineHeight};
      ${styles.typography.name.letterSpacing ? `letter-spacing: ${styles.typography.name.letterSpacing};` : ''}
      ${styles.typography.name.textAlign ? `text-align: ${styles.typography.name.textAlign};` : ''}
    }
    
    h2 {
      font-size: ${styles.typography.title.fontSize}px;
      font-weight: ${styles.typography.title.fontWeight};
      ${styles.typography.title.letterSpacing ? `letter-spacing: ${styles.typography.title.letterSpacing};` : ''}
      ${styles.typography.title.textAlign ? `text-align: ${styles.typography.title.textAlign};` : ''}
    }
    
    .company {
      font-size: ${styles.typography.company.fontSize}px;
      font-style: ${styles.typography.company.fontStyle || 'normal'};
      ${styles.typography.company.fontWeight ? `font-weight: ${styles.typography.company.fontWeight};` : ''}
      ${styles.typography.company.textTransform ? `text-transform: ${styles.typography.company.textTransform};` : ''}
      ${styles.typography.company.textAlign ? `text-align: ${styles.typography.company.textAlign};` : ''}
    }
    
    .contact-item {
      font-size: ${styles.typography.contact.fontSize}px;
      ${styles.typography.contact.fontWeight ? `font-weight: ${styles.typography.contact.fontWeight};` : ''}
      ${styles.typography.contact.display ? `display: ${styles.typography.contact.display};` : ''}
      ${styles.typography.contact.margin ? `margin: ${styles.typography.contact.margin};` : ''}
    }
  `;

  return {
    css,
    layout: layout.type,
    contentLayout: layout.contentLayout
  };
}

// Get random template for shuffle
export function getRandomTemplate() {
  const templates = loadTemplates();
  const randomIndex = Math.floor(Math.random() * templates.length);
  return templates[randomIndex];
}
