# 🚀 Deployment Guide

This guide covers deploying the Digital Business Card Generator to various platforms.

## 📋 Pre-deployment Checklist

- [ ] AWS S3 bucket created and configured
- [ ] Environment variables configured
- [ ] Dependencies installed and tested locally
- [ ] Code tested in production mode locally

## 🌐 Platform-Specific Deployments

### Vercel (Client) + Render (Server)

#### 1. Deploy Client to Vercel
```bash
cd client
npm install -g vercel
vercel login
vercel --prod
```

**Environment Variables in Vercel:**
- `NEXT_PUBLIC_API_URL`: Your deployed server URL

#### 2. Deploy Server to Render
1. Connect your GitHub repository to Render
2. Create a new Web Service
3. Configure:
   - **Build Command**: `cd server && npm install`
   - **Start Command**: `cd server && npm start`
   - **Environment**: Node.js

**Environment Variables in Render:**
```
PORT=10000
NODE_ENV=production
SERVER_BASE=https://your-app.onrender.com
CLIENT_URL=https://your-app.vercel.app
AWS_ACCESS_KEY=your_key
AWS_SECRET_KEY=your_secret
AWS_REGION=us-east-1
S3_BUCKET=your-bucket
SHARE_BASE_URL=https://your-app.vercel.app
```

### Railway (Full Stack)

#### 1. Deploy Both Services
```bash
npm install -g @railway/cli
railway login
railway init
railway up
```

#### 2. Configure Services
Create `railway.json`:
```json
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm start",
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### Heroku (Server) + Netlify (Client)

#### 1. Deploy Server to Heroku
```bash
cd server
heroku create your-app-name
heroku config:set NODE_ENV=production
heroku config:set AWS_ACCESS_KEY=your_key
# ... set all environment variables
git push heroku main
```

#### 2. Deploy Client to Netlify
```bash
cd client
npm run build
# Upload dist folder to Netlify or connect GitHub repo
```

### Docker Deployment

#### 1. Create Dockerfiles

**Client Dockerfile:**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

**Server Dockerfile:**
```dockerfile
FROM node:18-alpine
RUN apk add --no-cache chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]
```

#### 2. Docker Compose
```yaml
version: '3.8'
services:
  client:
    build: ./client
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:5000/api
    depends_on:
      - server

  server:
    build: ./server
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - AWS_ACCESS_KEY=${AWS_ACCESS_KEY}
      - AWS_SECRET_KEY=${AWS_SECRET_KEY}
      - S3_BUCKET=${S3_BUCKET}
```

## 🔧 AWS S3 Configuration

### 1. Create S3 Bucket
```bash
aws s3 mb s3://your-business-cards-bucket
```

### 2. Set Bucket Policy
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-business-cards-bucket/cards/*"
    },
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow", 
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-business-cards-bucket/qr/*"
    }
  ]
}
```

### 3. Enable CORS
```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "HEAD"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": []
  }
]
```

## 🔒 Security Considerations

### Production Environment Variables
- Use strong, unique secrets
- Enable HTTPS only
- Configure proper CORS origins
- Set up monitoring and logging

### Rate Limiting
The app includes built-in rate limiting, but consider adding:
- CloudFlare for DDoS protection
- API Gateway for additional throttling
- Redis for distributed rate limiting

## 📊 Monitoring & Analytics

### Recommended Tools
- **Error Tracking**: Sentry
- **Analytics**: Google Analytics, Mixpanel
- **Uptime Monitoring**: Pingdom, UptimeRobot
- **Performance**: New Relic, DataDog

### Health Check Endpoints
Add to `server/routes/health.js`:
```javascript
router.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version 
  });
});
```

## 🚀 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm run install:all
      
      - name: Build client
        run: cd client && npm run build
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          working-directory: ./client
```

## 🔧 Performance Optimization

### Client Optimizations
- Enable Next.js Image Optimization
- Implement lazy loading
- Use CDN for static assets
- Enable gzip compression

### Server Optimizations
- Use PM2 for process management
- Enable Redis caching
- Optimize Puppeteer settings
- Use connection pooling

## 🐛 Troubleshooting

### Common Deployment Issues

**Build Failures:**
- Check Node.js version compatibility
- Verify all dependencies are installed
- Check for missing environment variables

**Puppeteer Issues:**
- Install system dependencies
- Use correct Chromium path
- Set proper sandbox flags

**S3 Upload Failures:**
- Verify AWS credentials
- Check bucket permissions
- Ensure correct region configuration

### Debug Commands
```bash
# Check server logs
heroku logs --tail -a your-app-name

# Test API endpoints
curl https://your-api.com/api/health

# Check environment variables
heroku config -a your-app-name
```

---

**Need help?** Check the main README.md or create an issue in the repository.
