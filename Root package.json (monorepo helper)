{"name": "digital-business-card-generator", "version": "1.0.0", "description": "A modern, full-stack application that generates unique, professional business cards in seconds", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm install --production", "start": "concurrently \"npm run start:client\" \"npm run start:server\"", "start:client": "cd client && npm start", "start:server": "cd server && npm start", "install:all": "npm install && cd client && npm install && cd ../server && npm install", "setup": "npm run install:all && npm run setup:env", "setup:env": "cp server/.env.example server/.env && cp client/.env.local.example client/.env.local && echo 'Environment files created! Please edit them with your configuration.'", "clean": "rm -rf client/.next client/node_modules server/node_modules node_modules", "lint": "cd client && npm run lint", "test": "echo 'Tests not implemented yet'"}, "keywords": ["business-card", "generator", "nextjs", "express", "puppeteer", "aws-s3", "digital-card"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}