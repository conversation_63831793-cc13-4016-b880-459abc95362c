import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import archiver from 'archiver';
import { uploadToS3 } from '../utils/s3.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Generate Apple Wallet Pass (.pkpass)
export async function generateAppleWalletPass(cardData, cardId) {
  try {
    const passData = {
      formatVersion: 1,
      passTypeIdentifier: process.env.APPLE_PASS_TYPE_ID || "pass.com.yourcompany.businesscard",
      serialNumber: cardId,
      teamIdentifier: process.env.APPLE_TEAM_ID || "YOUR_TEAM_ID",
      organizationName: "Digital Business Cards",
      description: `Business Card - ${cardData.name}`,
      logoText: cardData.company || cardData.name,
      foregroundColor: "rgb(255, 255, 255)",
      backgroundColor: "rgb(60, 60, 60)",
      labelColor: "rgb(255, 255, 255)",
      generic: {
        primaryFields: [
          {
            key: "name",
            label: "Name",
            value: cardData.name
          }
        ],
        secondaryFields: [
          {
            key: "title",
            label: "Title", 
            value: cardData.title || ""
          },
          {
            key: "company",
            label: "Company",
            value: cardData.company || ""
          }
        ],
        auxiliaryFields: [
          {
            key: "phone",
            label: "Phone",
            value: cardData.phone || ""
          },
          {
            key: "email", 
            label: "Email",
            value: cardData.email || ""
          }
        ],
        backFields: [
          {
            key: "website",
            label: "Website",
            value: cardData.website || ""
          },
          {
            key: "qr",
            label: "Share Link",
            value: `${process.env.SHARE_BASE_URL || 'https://cards.yourdomain.com'}/card/${cardId}`
          }
        ]
      },
      barcodes: [
        {
          message: `${process.env.SHARE_BASE_URL || 'https://cards.yourdomain.com'}/card/${cardId}`,
          format: "PKBarcodeFormatQR",
          messageEncoding: "iso-8859-1"
        }
      ]
    };

    // Create temporary directory for pass files
    const tempDir = path.join(__dirname, '../temp', cardId);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Write pass.json
    fs.writeFileSync(
      path.join(tempDir, 'pass.json'),
      JSON.stringify(passData, null, 2)
    );

    // Create manifest.json (simplified - in production you'd need proper signing)
    const manifest = {
      "pass.json": "placeholder-hash"
    };
    
    fs.writeFileSync(
      path.join(tempDir, 'manifest.json'),
      JSON.stringify(manifest, null, 2)
    );

    // Create ZIP archive
    const zipBuffer = await createZipArchive(tempDir);
    
    // Upload to S3
    const passKey = `passes/${cardId}.pkpass`;
    const passUrl = await uploadToS3(zipBuffer, passKey, 'application/vnd.apple.pkpass');

    // Cleanup temp directory
    fs.rmSync(tempDir, { recursive: true, force: true });

    return passUrl;

  } catch (error) {
    console.error('Apple Wallet Pass generation error:', error);
    throw new Error('Failed to generate Apple Wallet Pass');
  }
}

// Generate Google Pay Pass (simplified JSON format)
export async function generateGooglePayPass(cardData, cardId) {
  try {
    const passData = {
      iss: process.env.GOOGLE_PAY_ISSUER_EMAIL || "<EMAIL>",
      aud: "google",
      typ: "savetowallet",
      iat: Math.floor(Date.now() / 1000),
      payload: {
        genericObjects: [
          {
            id: `${process.env.GOOGLE_PAY_ISSUER_ID || "your-issuer-id"}.${cardId}`,
            classId: `${process.env.GOOGLE_PAY_ISSUER_ID || "your-issuer-id"}.business_card_class`,
            genericType: "GENERIC_TYPE_UNSPECIFIED",
            hexBackgroundColor: "#3c3c3c",
            logo: {
              sourceUri: {
                uri: cardData.logo ? `${process.env.SERVER_BASE}${cardData.logo}` : "https://via.placeholder.com/100"
              }
            },
            cardTitle: {
              defaultValue: {
                language: "en",
                value: cardData.name
              }
            },
            subheader: {
              defaultValue: {
                language: "en", 
                value: cardData.title || ""
              }
            },
            header: {
              defaultValue: {
                language: "en",
                value: cardData.company || "Business Card"
              }
            },
            textModulesData: [
              {
                id: "contact_info",
                header: "Contact Information",
                body: `Phone: ${cardData.phone || 'N/A'}\nEmail: ${cardData.email || 'N/A'}\nWebsite: ${cardData.website || 'N/A'}`
              }
            ],
            linksModuleData: {
              uris: [
                {
                  uri: `${process.env.SHARE_BASE_URL || 'https://cards.yourdomain.com'}/card/${cardId}`,
                  description: "View Digital Card"
                }
              ]
            },
            barcode: {
              type: "QR_CODE",
              value: `${process.env.SHARE_BASE_URL || 'https://cards.yourdomain.com'}/card/${cardId}`
            }
          }
        ]
      }
    };

    // In production, you'd sign this JWT with your Google service account key
    // For now, return the unsigned payload
    const passKey = `passes/google-${cardId}.json`;
    const passBuffer = Buffer.from(JSON.stringify(passData, null, 2));
    const passUrl = await uploadToS3(passBuffer, passKey, 'application/json');

    return passUrl;

  } catch (error) {
    console.error('Google Pay Pass generation error:', error);
    throw new Error('Failed to generate Google Pay Pass');
  }
}

// Helper function to create ZIP archive
function createZipArchive(sourceDir) {
  return new Promise((resolve, reject) => {
    const archive = archiver('zip', { zlib: { level: 9 } });
    const chunks = [];

    archive.on('data', chunk => chunks.push(chunk));
    archive.on('end', () => resolve(Buffer.concat(chunks)));
    archive.on('error', reject);

    // Add all files from source directory
    archive.directory(sourceDir, false);
    archive.finalize();
  });
}
