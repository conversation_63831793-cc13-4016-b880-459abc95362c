import crypto from 'crypto';

// 20 Color Palettes
export const COLOR_PALETTES = [
  { name: "Ocean", primary: "#0077be", secondary: "#00a8cc", accent: "#ffd23f", text: "#ffffff" },
  { name: "<PERSON>", primary: "#2d5016", secondary: "#4a7c59", accent: "#f4a261", text: "#ffffff" },
  { name: "Sunset", primary: "#e63946", secondary: "#f77f00", accent: "#fcbf49", text: "#ffffff" },
  { name: "<PERSON>", primary: "#4c1d95", secondary: "#7c3aed", accent: "#fbbf24", text: "#ffffff" },
  { name: "<PERSON>", primary: "#dc2626", secondary: "#f97316", accent: "#fde047", text: "#ffffff" },
  { name: "<PERSON>", primary: "#059669", secondary: "#10b981", accent: "#fde68a", text: "#ffffff" },
  { name: "Slate", primary: "#374151", secondary: "#6b7280", accent: "#f59e0b", text: "#ffffff" },
  { name: "Rose", primary: "#be185d", secondary: "#ec4899", accent: "#fde047", text: "#ffffff" },
  { name: "Indigo", primary: "#3730a3", secondary: "#6366f1", accent: "#fbbf24", text: "#ffffff" },
  { name: "Emerald", primary: "#047857", secondary: "#10b981", accent: "#fed7aa", text: "#ffffff" },
  { name: "Amber", primary: "#d97706", secondary: "#f59e0b", accent: "#fef3c7", text: "#1f2937" },
  { name: "Teal", primary: "#0f766e", secondary: "#14b8a6", accent: "#fde68a", text: "#ffffff" },
  { name: "Purple", primary: "#6b21a8", secondary: "#a855f7", accent: "#fde047", text: "#ffffff" },
  { name: "Pink", primary: "#be185d", secondary: "#ec4899", accent: "#fed7aa", text: "#ffffff" },
  { name: "Blue", primary: "#1e40af", secondary: "#3b82f6", accent: "#fbbf24", text: "#ffffff" },
  { name: "Green", primary: "#166534", secondary: "#22c55e", accent: "#fed7aa", text: "#ffffff" },
  { name: "Red", primary: "#b91c1c", secondary: "#ef4444", accent: "#fde047", text: "#ffffff" },
  { name: "Gray", primary: "#374151", secondary: "#6b7280", accent: "#f59e0b", text: "#ffffff" },
  { name: "Cyan", primary: "#0e7490", secondary: "#06b6d4", accent: "#fde047", text: "#ffffff" },
  { name: "Lime", primary: "#365314", secondary: "#65a30d", accent: "#fed7aa", text: "#ffffff" }
];

// 10 Font Pairs
export const FONT_PAIRS = [
  { name: "Modern", heading: "'Segoe UI', sans-serif", body: "'Segoe UI', sans-serif" },
  { name: "Classic", heading: "'Times New Roman', serif", body: "'Georgia', serif" },
  { name: "Clean", heading: "'Helvetica Neue', sans-serif", body: "'Arial', sans-serif" },
  { name: "Tech", heading: "'Consolas', monospace", body: "'Courier New', monospace" },
  { name: "Elegant", heading: "'Playfair Display', serif", body: "'Source Sans Pro', sans-serif" },
  { name: "Bold", heading: "'Impact', sans-serif", body: "'Verdana', sans-serif" },
  { name: "Minimal", heading: "'Roboto', sans-serif", body: "'Open Sans', sans-serif" },
  { name: "Creative", heading: "'Comfortaa', cursive", body: "'Lato', sans-serif" },
  { name: "Professional", heading: "'Montserrat', sans-serif", body: "'Source Sans Pro', sans-serif" },
  { name: "Friendly", heading: "'Nunito', sans-serif", body: "'Nunito Sans', sans-serif" }
];

// 3 Layout Templates
export const LAYOUT_TEMPLATES = [
  {
    name: "Split",
    leftWidth: "35%",
    rightWidth: "65%",
    photoPosition: "left",
    logoPosition: "top-right",
    contentLayout: "vertical"
  },
  {
    name: "Header",
    leftWidth: "100%",
    rightWidth: "100%",
    photoPosition: "top-center",
    logoPosition: "top-left",
    contentLayout: "horizontal"
  },
  {
    name: "Sidebar",
    leftWidth: "25%",
    rightWidth: "75%",
    photoPosition: "left-top",
    logoPosition: "right-bottom",
    contentLayout: "vertical"
  }
];

// Hash function to generate consistent styles based on seed
function hashSeed(seed) {
  return crypto.createHash('md5').update(seed).digest('hex');
}

// Generate style based on seed (email or random)
export function generateStyle(seed, shuffle = false) {
  const hash = hashSeed(seed + (shuffle ? Date.now() : ''));
  
  // Convert hash to numbers for selection
  const colorIndex = parseInt(hash.substring(0, 2), 16) % COLOR_PALETTES.length;
  const fontIndex = parseInt(hash.substring(2, 4), 16) % FONT_PAIRS.length;
  const layoutIndex = parseInt(hash.substring(4, 6), 16) % LAYOUT_TEMPLATES.length;
  
  return {
    colors: COLOR_PALETTES[colorIndex],
    fonts: FONT_PAIRS[fontIndex],
    layout: LAYOUT_TEMPLATES[layoutIndex],
    hash: hash.substring(0, 8) // Short hash for reference
  };
}

// Generate random hue for fallback
export function generateRandomHue() {
  return Math.floor(Math.random() * 360);
}
