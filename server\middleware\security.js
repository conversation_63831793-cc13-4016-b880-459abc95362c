import rateLimit from 'express-rate-limit';
import helmet from 'helmet';

// Rate limiting middleware
export const createRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: {
    error: 'Too many card generation requests, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Shuffle rate limiting (more lenient)
export const shuffleRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 20, // 20 shuffles per minute
  message: {
    error: 'Too many shuffle requests, please slow down.',
    retryAfter: '1 minute'
  }
});

// Security headers
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "fonts.googleapis.com"],
      fontSrc: ["'self'", "fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
    },
  },
  crossOriginEmbedderPolicy: false
});

// Input sanitization
export function sanitizeInput(input) {
  if (typeof input !== 'string') return input;
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 200); // Limit length
}

// Validate file types more strictly
export function validateFileType(file) {
  const allowedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ];
  
  return allowedTypes.includes(file.mimetype);
}

// Validate total request size
export function validateRequestSize(req, res, next) {
  const maxSize = 10 * 1024 * 1024; // 10MB total
  
  if (req.headers['content-length'] > maxSize) {
    return res.status(413).json({
      error: 'Request too large. Maximum total size is 10MB.'
    });
  }
  
  next();
}
