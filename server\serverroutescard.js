import express from "express";
import multer from "multer";
import { v4 as uuid } from "uuid";
import path from "path";
import fs from "fs";
import generateCard from "../services/generator.js";

const router = express.Router();

// Disk storage
const storage = multer.diskStorage({
  destination: (_, __, cb) => {
    const dir = "server/uploads";
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
    cb(null, dir);
  },
  filename: (_, file, cb) => cb(null, uuid() + path.extname(file.originalname))
});
const upload = multer({ storage });

router.post(
  "/card",
  upload.fields([
    { name: "photo", maxCount: 1 },
    { name: "logo", maxCount: 1 },
    { name: "products", maxCount: 3 }
  ]),
  async (req, res) => {
    try {
      const urls = {
        photo: req.files.photo ? `/uploads/${req.files.photo[0].filename}` : null,
        logo: req.files.logo ? `/uploads/${req.files.logo[0].filename}` : null,
        products: (req.files.products || []).map(
          (f) => `/uploads/${f.filename}`
        )
      };
      const cardData = { ...req.body, ...urls };
      const { pngUrl, pdfUrl, shareLink } = await generateCard(cardData);
      res.json({ pngUrl, pdfUrl, shareLink });
    } catch (err) {
      console.error(err);
      res.status(500).send("Generation failed");
    }
  }
);

export default router;