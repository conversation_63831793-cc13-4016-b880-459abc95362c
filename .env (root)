# This file shows example environment variables
# Copy server/.env.example to server/.env and fill in your values
# Copy client/.env.local.example to client/.env.local and fill in your values

# Quick Start (Development):
# 1. Set up AWS S3 bucket and get credentials
# 2. Copy the example files and update with your values
# 3. Run: npm run dev

# For production deployment:
# 1. Update SERVER_BASE and CLIENT_URL to your domain
# 2. Set NODE_ENV=production
# 3. Configure your hosting platform with environment variables