import express from 'express';
import fs from 'fs';
import path from 'path';

const router = express.Router();

// Health check endpoint
router.get('/health', (req, res) => {
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  };

  try {
    // Check if uploads directory exists
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const uploadsExists = fs.existsSync(uploadsDir);
    
    healthCheck.services = {
      filesystem: uploadsExists ? 'healthy' : 'warning',
      s3: process.env.S3_BUCKET ? 'configured' : 'not_configured',
      puppeteer: 'ready'
    };

    res.status(200).json(healthCheck);
  } catch (error) {
    healthCheck.status = 'unhealthy';
    healthCheck.error = error.message;
    res.status(503).json(healthCheck);
  }
});

// System info endpoint (development only)
router.get('/info', (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).json({ error: 'Not found' });
  }

  const systemInfo = {
    node_version: process.version,
    platform: process.platform,
    arch: process.arch,
    memory: process.memoryUsage(),
    env: {
      NODE_ENV: process.env.NODE_ENV,
      PORT: process.env.PORT,
      S3_BUCKET: process.env.S3_BUCKET ? '***configured***' : 'not_set',
      AWS_REGION: process.env.AWS_REGION || 'not_set'
    }
  };

  res.json(systemInfo);
});

export default router;
