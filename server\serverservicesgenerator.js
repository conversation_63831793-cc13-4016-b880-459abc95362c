import puppeteer from "puppeteer";
import path from "path";
import fs from "fs";
import { uploadToS3 } from "../utils/s3.js";

export default async function generateCard(data) {
  const browser = await puppeteer.launch({ headless: "new", args: ["--no-sandbox"] });
  const page = await browser.newPage();

  // Build HTML
  const html = buildHTML(data);
  await page.setContent(html, { waitUntil: "networkidle0" });

  // Screenshot PNG
  const pngBuffer = await page.screenshot({ type: "png", clip: { x: 0, y: 0, width: 1050, height: 600 } });
  const pngKey = `cards/${Date.now()}.png`;
  await uploadToS3(pngBuffer, pngKey, "image/png");
  const pngUrl = `https://${process.env.S3_BUCKET}.s3.amazonaws.com/${pngKey}`;

  // PDF (optional)
  await page.addStyleTag({ content: "body { zoom: 1.25 }" }); // fit 300 dpi
  const pdfBuffer = await page.pdf({ width: "3.5in", height: "2in", printBackground: true });
  const pdfKey = pngKey.replace(".png", ".pdf");
  await uploadToS3(pdfBuffer, pdfKey, "application/pdf");
  const pdfUrl = pdfUrl = `https://${process.env.S3_BUCKET}.s3.amazonaws.com/${pdfKey}`;

  await browser.close();

  // Return share link (same as png)
  const shareLink = pngUrl;
  return { pngUrl, pdfUrl, shareLink };
}

function buildHTML({ name, title, phone, email, website, photo, logo, products }) {
  // Simple random color seed
  const hue = Math.floor(Math.random() * 360);
  const accent = `hsl(${hue},70%,50%)`;

  const img = (src) =>
    src ? `<img src="${process.env.SERVER_BASE || "http://localhost:5000"}${src}" />` : "";

  const productsHTML = products
    .slice(0, 3)
    .map((p) => `<div class="prod">${img(p)}</div>`)
    .join("");

  return `
<html>
<head>
  <meta charset="UTF-8" />
  <style>
    body { margin:0; width:1050px; height:600px; font-family: 'Segoe UI', sans-serif; display:flex; align-items:center; justify-content:center; background:#fafafa; }
    .card { width:1050px; height:600px; border:1px solid #ddd; display:flex; background:white; position:relative; }
    .left { width:35%; background:${accent}; display:flex; flex-direction:column; align-items:center; justify-content:center; color:#fff; padding:30px; }
    .left img { width:180px; height:180px; object-fit:cover; border-radius:50%; margin-bottom:20px; }
    .right { width:65%; padding:60px; display:flex; flex-direction:column; justify-content:center; }
    h1 { margin:0; font-size:52px; }
    h2 { margin:5px 0 25px; font-weight:400; color:#555; }
    p { margin:8px 0; font-size:28px; color:#333; }
    .logo { position:absolute; top:30px; right:30px; width:120px; height:120px; object-fit:contain; }
    .prods { display:flex; margin-top:30px; gap:15px; }
    .prod img { width:100px; height:80px; object-fit:cover; border-radius:8px; }
  </style>
</head>
<body>
  <div class="card">
    <div class="left">
      ${img(photo)}
    </div>
    <div class="right">
      ${img(logo)}
      <h1>${name}</h1>
      <h2>${title}</h2>
      <p>📱 ${phone}</p>
      <p>✉️ ${email}</p>
      <p>🌐 ${website}</p>
      <div class="prods">${productsHTML}</div>
    </div>
  </div>
</body>
</html>`;
}