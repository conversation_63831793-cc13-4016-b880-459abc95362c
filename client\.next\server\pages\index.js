/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.js */ \"./pages/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_index_js__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_index_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTJnBhZ2U9JTJGJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGcGFnZXMlNUNpbmRleC5qcyZhYnNvbHV0ZUFwcFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2FwcCZhYnNvbHV0ZURvY3VtZW50UGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfZG9jdW1lbnQmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ2hDO0FBQ0w7QUFDMUQ7QUFDb0Q7QUFDVjtBQUMxQztBQUM4QztBQUM5QztBQUNBLGlFQUFlLHdFQUFLLENBQUMsNENBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sdUJBQXVCLHdFQUFLLENBQUMsNENBQVE7QUFDckMsdUJBQXVCLHdFQUFLLENBQUMsNENBQVE7QUFDckMsMkJBQTJCLHdFQUFLLENBQUMsNENBQVE7QUFDekMsZUFBZSx3RUFBSyxDQUFDLDRDQUFRO0FBQzdCLHdCQUF3Qix3RUFBSyxDQUFDLDRDQUFRO0FBQzdDO0FBQ08sZ0NBQWdDLHdFQUFLLENBQUMsNENBQVE7QUFDOUMsZ0NBQWdDLHdFQUFLLENBQUMsNENBQVE7QUFDOUMsaUNBQWlDLHdFQUFLLENBQUMsNENBQVE7QUFDL0MsZ0NBQWdDLHdFQUFLLENBQUMsNENBQVE7QUFDOUMsb0NBQW9DLHdFQUFLLENBQUMsNENBQVE7QUFDekQ7QUFDTyx3QkFBd0IseUdBQWdCO0FBQy9DO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsV0FBVztBQUNYLGdCQUFnQjtBQUNoQixLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQsaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jbGllbnQvPzJjZTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSBhcHAgYW5kIGRvY3VtZW50IG1vZHVsZXMuXG5pbXBvcnQgRG9jdW1lbnQgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fZG9jdW1lbnRcIjtcbmltcG9ydCBBcHAgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fYXBwXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlc1xcXFxpbmRleC5qc1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBjb21wb25lbnQgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsIFwiZGVmYXVsdFwiKTtcbi8vIFJlLWV4cG9ydCBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsIFwiZ2V0U3RhdGljUHJvcHNcIik7XG5leHBvcnQgY29uc3QgZ2V0U3RhdGljUGF0aHMgPSBob2lzdCh1c2VybGFuZCwgXCJnZXRTdGF0aWNQYXRoc1wiKTtcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJnZXRTZXJ2ZXJTaWRlUHJvcHNcIik7XG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsIFwiY29uZmlnXCIpO1xuZXhwb3J0IGNvbnN0IHJlcG9ydFdlYlZpdGFscyA9IGhvaXN0KHVzZXJsYW5kLCBcInJlcG9ydFdlYlZpdGFsc1wiKTtcbi8vIFJlLWV4cG9ydCBsZWdhY3kgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCBcInVuc3RhYmxlX2dldFN0YXRpY1Byb3BzXCIpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U3RhdGljUGF0aHNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zXCIpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclByb3BzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U2VydmVyUHJvcHNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzXCIpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVMsXG4gICAgICAgIHBhZ2U6IFwiL2luZGV4XCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIlxuICAgIH0sXG4gICAgY29tcG9uZW50czoge1xuICAgICAgICBBcHAsXG4gICAgICAgIERvY3VtZW50XG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\_app.js\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUErQjtBQUVoQixTQUFTQSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFO0lBQ2xELHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NsaWVudC8uL3BhZ2VzL19hcHAuanM/ZTBhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgXCIuLi9zdHlsZXMvZ2xvYmFscy5jc3NcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPjtcbn1cbiJdLCJuYW1lcyI6WyJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/index.js":
/*!************************!*\
  !*** ./pages/index.js ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dropzone */ \"react-dropzone\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_dropzone__WEBPACK_IMPORTED_MODULE_3__, axios__WEBPACK_IMPORTED_MODULE_4__]);\n([react_dropzone__WEBPACK_IMPORTED_MODULE_3__, axios__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction Home() {\n    const [fields, setFields] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        name: \"\",\n        title: \"\",\n        company: \"\",\n        phone: \"\",\n        email: \"\",\n        website: \"\"\n    });\n    const [photo, setPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [logo, setLogo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [products, setProds] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentStyle, setCurrentStyle] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isShuffling, setIsShuffling] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [generatedCard, setGeneratedCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const { getRootProps: gpPhoto, getInputProps: giPhoto } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_3__.useDropzone)({\n        accept: {\n            \"image/*\": []\n        },\n        maxFiles: 1,\n        maxSize: 2 * 1024 * 1024,\n        onDrop: (f)=>setPhoto(f[0])\n    });\n    const { getRootProps: gpLogo, getInputProps: giLogo } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_3__.useDropzone)({\n        accept: {\n            \"image/*\": []\n        },\n        maxFiles: 1,\n        maxSize: 2 * 1024 * 1024,\n        onDrop: (f)=>setLogo(f[0])\n    });\n    const { getRootProps: gpProds, getInputProps: giProds } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_3__.useDropzone)({\n        accept: {\n            \"image/*\": []\n        },\n        maxFiles: 3,\n        maxSize: 2 * 1024 * 1024,\n        onDrop: setProds\n    });\n    const handleChange = (e)=>setFields({\n            ...fields,\n            [e.target.name]: e.target.value\n        });\n    const validateEmail = (email)=>{\n        const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return re.test(email);\n    };\n    const validatePhone = (phone)=>{\n        const re = /^[\\+]?[1-9][\\d]{0,15}$/;\n        return re.test(phone.replace(/[\\s\\-\\(\\)]/g, \"\"));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!fields.name || !fields.email) {\n            alert(\"Name and email are required\");\n            return;\n        }\n        if (!validateEmail(fields.email)) {\n            alert(\"Please enter a valid email address\");\n            return;\n        }\n        if (fields.phone && !validatePhone(fields.phone)) {\n            alert(\"Please enter a valid phone number\");\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const form = new FormData();\n            Object.keys(fields).forEach((k)=>form.append(k, fields[k]));\n            if (photo) form.append(\"photo\", photo);\n            if (logo) form.append(\"logo\", logo);\n            products.forEach((f)=>form.append(\"products\", f));\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(`${\"http://localhost:5000/api\"}/card`, form, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                }\n            });\n            // Open PNG in new tab\n            window.open(data.pngUrl, \"_blank\");\n            // Show success modal instead of alert\n            showSuccessModal(data);\n        } catch (error) {\n            console.error(\"Generation failed:\", error);\n            alert(\"Failed to generate card. Please try again.\");\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const shuffleStyle = async ()=>{\n        if (!fields.email) {\n            alert(\"Please enter your email first to shuffle styles\");\n            return;\n        }\n        setIsShuffling(true);\n        try {\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(`${\"http://localhost:5000/api\"}/shuffle`, {\n                email: fields.email\n            });\n            setCurrentStyle(data.style);\n            console.log(\"New style:\", data.style);\n        } catch (error) {\n            console.error(\"Shuffle failed:\", error);\n            alert(\"Failed to shuffle style. Please try again.\");\n        } finally{\n            setIsShuffling(false);\n        }\n    };\n    const showSuccessModal = (data)=>{\n        setGeneratedCard(data);\n        setShowModal(true);\n    };\n    const closeModal = ()=>{\n        setShowModal(false);\n        setGeneratedCard(null);\n    };\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text).then(()=>{\n            alert(\"Copied to clipboard!\");\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"jsx-30c91ffcfad3d98c\",\n                children: \"Create Your Digital Business Card\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"subtitle\",\n                children: \"Generate a unique, professional business card in seconds\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"jsx-30c91ffcfad3d98c\",\n                children: [\n                    [\n                        \"name\",\n                        \"title\",\n                        \"company\",\n                        \"phone\",\n                        \"email\",\n                        \"website\"\n                    ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"field-group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: field,\n                                    className: \"jsx-30c91ffcfad3d98c\",\n                                    children: field.charAt(0).toUpperCase() + field.slice(1)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: field,\n                                    name: field,\n                                    type: field === \"email\" ? \"email\" : field === \"phone\" ? \"tel\" : \"text\",\n                                    placeholder: `Enter your ${field}`,\n                                    value: fields[field],\n                                    onChange: handleChange,\n                                    required: field === \"name\" || field === \"email\",\n                                    className: \"jsx-30c91ffcfad3d98c\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, field, true, {\n                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"upload-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ...gpPhoto(),\n                                className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"drop-zone\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ...giPhoto(),\n                                        className: \"jsx-30c91ffcfad3d98c\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"drop-content\",\n                                        children: photo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-30c91ffcfad3d98c\",\n                                            children: [\n                                                \"✓ \",\n                                                photo.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"drop-icon\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\",\n                                                    children: \"Upload Photo\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\",\n                                                    children: \"Max 2MB\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ...gpLogo(),\n                                className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"drop-zone\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ...giLogo(),\n                                        className: \"jsx-30c91ffcfad3d98c\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"drop-content\",\n                                        children: logo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-30c91ffcfad3d98c\",\n                                            children: [\n                                                \"✓ \",\n                                                logo.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"drop-icon\",\n                                                    children: \"\\uD83C\\uDFE2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\",\n                                                    children: \"Upload Logo\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\",\n                                                    children: \"Max 2MB\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ...gpProds(),\n                                className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"drop-zone\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ...giProds(),\n                                        className: \"jsx-30c91ffcfad3d98c\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"drop-content\",\n                                        children: products.length ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-30c91ffcfad3d98c\",\n                                            children: [\n                                                \"✓ \",\n                                                products.length,\n                                                \" product image\",\n                                                products.length > 1 ? \"s\" : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"drop-icon\",\n                                                    children: \"\\uD83D\\uDCE6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\",\n                                                    children: \"Upload Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\",\n                                                    children: \"Max 3 images, 2MB each\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"button-group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: shuffleStyle,\n                                disabled: isShuffling || !fields.email,\n                                className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"shuffle-btn\",\n                                children: isShuffling ? \"Shuffling...\" : \"\\uD83C\\uDFB2 Shuffle Style\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isGenerating,\n                                className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"generate-btn\",\n                                children: isGenerating ? \"Generating...\" : \"Generate & Download\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    currentStyle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"style-preview\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"jsx-30c91ffcfad3d98c\",\n                                children: \"Current Style Preview\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"style-info\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"color-palette\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-30c91ffcfad3d98c\",\n                                                children: [\n                                                    \"Colors: \",\n                                                    currentStyle.colors.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"color-swatches\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: currentStyle.colors.primary\n                                                        },\n                                                        title: \"Primary\",\n                                                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"color-swatch\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: currentStyle.colors.secondary\n                                                        },\n                                                        title: \"Secondary\",\n                                                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"color-swatch\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: currentStyle.colors.accent\n                                                        },\n                                                        title: \"Accent\",\n                                                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"color-swatch\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"font-info\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-30c91ffcfad3d98c\",\n                                            children: [\n                                                \"Font: \",\n                                                currentStyle.fonts.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"layout-info\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-30c91ffcfad3d98c\",\n                                            children: [\n                                                \"Layout: \",\n                                                currentStyle.layout.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            showModal && generatedCard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: closeModal,\n                className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"modal-overlay\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onClick: (e)=>e.stopPropagation(),\n                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"modal-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"modal-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"jsx-30c91ffcfad3d98c\",\n                                    children: \"\\uD83C\\uDF89 Card Generated Successfully!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeModal,\n                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"close-btn\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                            lineNumber: 267,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"modal-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"card-preview\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: generatedCard.pngUrl,\n                                        alt: \"Generated Business Card\",\n                                        className: \"jsx-30c91ffcfad3d98c\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"download-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-30c91ffcfad3d98c\",\n                                            children: \"\\uD83D\\uDCE5 Downloads\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"download-links\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: generatedCard.pngUrl,\n                                                    target: \"_blank\",\n                                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"download-btn png\",\n                                                    children: \"\\uD83D\\uDDBC️ PNG Image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: generatedCard.pdfUrl,\n                                                    target: \"_blank\",\n                                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"download-btn pdf\",\n                                                    children: \"\\uD83D\\uDCC4 PDF Document\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"share-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-30c91ffcfad3d98c\",\n                                            children: \"\\uD83D\\uDD17 Share & Save\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"share-links\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"share-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-30c91ffcfad3d98c\",\n                                                            children: \"Share Link:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"copy-group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: generatedCard.shareLink,\n                                                                    readOnly: true,\n                                                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"copy-input\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>copyToClipboard(generatedCard.shareLink),\n                                                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"copy-btn\",\n                                                                    children: \"\\uD83D\\uDCCB\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"wallet-options\",\n                                                    children: [\n                                                        generatedCard.appleWalletUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: generatedCard.appleWalletUrl,\n                                                            className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"wallet-btn apple\",\n                                                            children: \"\\uD83D\\uDCF1 Add to Apple Wallet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        generatedCard.googlePayUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: generatedCard.googlePayUrl,\n                                                            className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"wallet-btn google\",\n                                                            children: \"\\uD83D\\uDCB3 Save to Google Pay\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"qr-section\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-30c91ffcfad3d98c\",\n                                            children: \"\\uD83D\\uDCF1 QR Code\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: generatedCard.qrCodeUrl,\n                                            alt: \"QR Code\",\n                                            className: \"jsx-30c91ffcfad3d98c\" + \" \" + \"qr-code\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-30c91ffcfad3d98c\",\n                                            children: \"Scan to share your card instantly\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                    lineNumber: 266,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"30c91ffcfad3d98c\",\n                children: '.container.jsx-30c91ffcfad3d98c{max-width:700px;margin:50px auto;padding:20px;font-family:\"Segoe UI\",Tahoma,Geneva,Verdana,sans-serif;background:white;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;-webkit-box-shadow:0 4px 6px rgba(0,0,0,.1);-moz-box-shadow:0 4px 6px rgba(0,0,0,.1);box-shadow:0 4px 6px rgba(0,0,0,.1)}h1.jsx-30c91ffcfad3d98c{text-align:center;color:#333;margin-bottom:10px}.subtitle.jsx-30c91ffcfad3d98c{text-align:center;color:#666;margin-bottom:30px}.field-group.jsx-30c91ffcfad3d98c{margin-bottom:20px}label.jsx-30c91ffcfad3d98c{display:block;margin-bottom:5px;font-weight:600;color:#555}input.jsx-30c91ffcfad3d98c{width:100%;padding:12px;border:2px solid#e1e5e9;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-size:16px;-webkit-transition:border-color.3s;-moz-transition:border-color.3s;-o-transition:border-color.3s;transition:border-color.3s}input.jsx-30c91ffcfad3d98c:focus{outline:none;border-color:#4f46e5}.upload-section.jsx-30c91ffcfad3d98c{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin:30px 0}.drop-zone.jsx-30c91ffcfad3d98c{border:2px dashed#d1d5db;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;padding:20px;text-align:center;cursor:pointer;-webkit-transition:all.3s;-moz-transition:all.3s;-o-transition:all.3s;transition:all.3s;background:#f9fafb}.drop-zone.jsx-30c91ffcfad3d98c:hover{border-color:#4f46e5;background:#f0f9ff}.drop-content.jsx-30c91ffcfad3d98c{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:5px}.drop-icon.jsx-30c91ffcfad3d98c{font-size:24px}small.jsx-30c91ffcfad3d98c{color:#6b7280;font-size:12px}.button-group.jsx-30c91ffcfad3d98c{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:15px;margin-top:30px}.shuffle-btn.jsx-30c91ffcfad3d98c{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;padding:12px 24px;background:#f3f4f6;border:2px solid#d1d5db;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-size:16px;cursor:pointer;-webkit-transition:all.3s;-moz-transition:all.3s;-o-transition:all.3s;transition:all.3s}.shuffle-btn.jsx-30c91ffcfad3d98c:hover{background:#e5e7eb;border-color:#9ca3af}.generate-btn.jsx-30c91ffcfad3d98c{-webkit-box-flex:2;-webkit-flex:2;-moz-box-flex:2;-ms-flex:2;flex:2;padding:12px 24px;background:#4f46e5;color:white;border:none;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;font-size:16px;font-weight:600;cursor:pointer;-webkit-transition:background.3s;-moz-transition:background.3s;-o-transition:background.3s;transition:background.3s}.generate-btn.jsx-30c91ffcfad3d98c:hover:not(:disabled){background:#4338ca}.generate-btn.jsx-30c91ffcfad3d98c:disabled{background:#9ca3af;cursor:not-allowed}.shuffle-btn.jsx-30c91ffcfad3d98c:disabled{background:#f3f4f6;color:#9ca3af;cursor:not-allowed}.style-preview.jsx-30c91ffcfad3d98c{margin-top:30px;padding:20px;background:#f8fafc;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;border:1px solid#e2e8f0}.style-preview.jsx-30c91ffcfad3d98c h3.jsx-30c91ffcfad3d98c{margin:0 0 15px 0;color:#374151;font-size:18px}.style-info.jsx-30c91ffcfad3d98c{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:10px}.color-palette.jsx-30c91ffcfad3d98c{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:10px}.color-swatches.jsx-30c91ffcfad3d98c{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:5px}.color-swatch.jsx-30c91ffcfad3d98c{width:20px;height:20px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;border:1px solid#d1d5db;cursor:help}.font-info.jsx-30c91ffcfad3d98c,.layout-info.jsx-30c91ffcfad3d98c{color:#6b7280;font-size:14px}.modal-overlay.jsx-30c91ffcfad3d98c{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.7);display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;z-index:1000;padding:20px}.modal-content.jsx-30c91ffcfad3d98c{background:white;-webkit-border-radius:16px;-moz-border-radius:16px;border-radius:16px;max-width:600px;width:100%;max-height:90vh;overflow-y:auto;-webkit-box-shadow:0 20px 25px -5px rgba(0,0,0,.1);-moz-box-shadow:0 20px 25px -5px rgba(0,0,0,.1);box-shadow:0 20px 25px -5px rgba(0,0,0,.1)}.modal-header.jsx-30c91ffcfad3d98c{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:24px 24px 0;border-bottom:1px solid#e5e7eb;margin-bottom:24px}.modal-header.jsx-30c91ffcfad3d98c h2.jsx-30c91ffcfad3d98c{margin:0;color:#1f2937;font-size:24px}.close-btn.jsx-30c91ffcfad3d98c{background:none;border:none;font-size:32px;cursor:pointer;color:#6b7280;padding:0;width:40px;height:40px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-transition:all.2s;-moz-transition:all.2s;-o-transition:all.2s;transition:all.2s}.close-btn.jsx-30c91ffcfad3d98c:hover{background:#f3f4f6;color:#374151}.modal-body.jsx-30c91ffcfad3d98c{padding:0 24px 24px}.card-preview.jsx-30c91ffcfad3d98c{text-align:center;margin-bottom:24px}.card-preview.jsx-30c91ffcfad3d98c img.jsx-30c91ffcfad3d98c{max-width:100%;height:auto;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;-webkit-box-shadow:0 4px 6px rgba(0,0,0,.1);-moz-box-shadow:0 4px 6px rgba(0,0,0,.1);box-shadow:0 4px 6px rgba(0,0,0,.1)}.download-section.jsx-30c91ffcfad3d98c,.share-section.jsx-30c91ffcfad3d98c,.qr-section.jsx-30c91ffcfad3d98c{margin-bottom:24px}.download-section.jsx-30c91ffcfad3d98c h3.jsx-30c91ffcfad3d98c,.share-section.jsx-30c91ffcfad3d98c h3.jsx-30c91ffcfad3d98c,.qr-section.jsx-30c91ffcfad3d98c h3.jsx-30c91ffcfad3d98c{margin:0 0 12px 0;color:#374151;font-size:18px}.download-links.jsx-30c91ffcfad3d98c{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px}.download-btn.jsx-30c91ffcfad3d98c{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;padding:12px 16px;text-decoration:none;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;text-align:center;font-weight:600;-webkit-transition:all.2s;-moz-transition:all.2s;-o-transition:all.2s;transition:all.2s}.download-btn.png.jsx-30c91ffcfad3d98c{background:#3b82f6;color:white}.download-btn.png.jsx-30c91ffcfad3d98c:hover{background:#2563eb}.download-btn.pdf.jsx-30c91ffcfad3d98c{background:#ef4444;color:white}.download-btn.pdf.jsx-30c91ffcfad3d98c:hover{background:#dc2626}.share-item.jsx-30c91ffcfad3d98c{margin-bottom:16px}.share-item.jsx-30c91ffcfad3d98c span.jsx-30c91ffcfad3d98c{display:block;margin-bottom:8px;font-weight:600;color:#374151}.copy-group.jsx-30c91ffcfad3d98c{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:8px}.copy-input.jsx-30c91ffcfad3d98c{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;padding:8px 12px;border:1px solid#d1d5db;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:14px;background:#f9fafb}.copy-btn.jsx-30c91ffcfad3d98c{padding:8px 12px;background:#6b7280;color:white;border:none;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;cursor:pointer;-webkit-transition:background.2s;-moz-transition:background.2s;-o-transition:background.2s;transition:background.2s}.copy-btn.jsx-30c91ffcfad3d98c:hover{background:#4b5563}.wallet-options.jsx-30c91ffcfad3d98c{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px;margin-top:12px}.wallet-btn.jsx-30c91ffcfad3d98c{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;padding:10px 16px;text-decoration:none;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;text-align:center;font-weight:600;-webkit-transition:all.2s;-moz-transition:all.2s;-o-transition:all.2s;transition:all.2s}.wallet-btn.apple.jsx-30c91ffcfad3d98c{background:#000;color:white}.wallet-btn.apple.jsx-30c91ffcfad3d98c:hover{background:#333}.wallet-btn.google.jsx-30c91ffcfad3d98c{background:#4285f4;color:white}.wallet-btn.google.jsx-30c91ffcfad3d98c:hover{background:#3367d6}.qr-section.jsx-30c91ffcfad3d98c{text-align:center}.qr-code.jsx-30c91ffcfad3d98c{width:150px;height:150px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;margin:12px 0}.qr-section.jsx-30c91ffcfad3d98c p.jsx-30c91ffcfad3d98c{color:#6b7280;font-size:14px;margin:0}'\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Repo structure\\\\client\\\\pages\\\\index.js\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/index.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-jsx/style");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "react-dropzone":
/*!*********************************!*\
  !*** external "react-dropzone" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-dropzone");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();