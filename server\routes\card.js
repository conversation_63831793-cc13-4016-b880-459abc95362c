import express from "express";
import multer from "multer";
import { v4 as uuid } from "uuid";
import path from "path";
import fs from "fs";
import generateCard from "../services/generator.js";
import {
  createRateLimit,
  shuffleRateLimit,
  sanitizeInput,
  validateFileType,
  validateRequestSize
} from "../middleware/security.js";

const router = express.Router();

// Disk storage with file size limits
const storage = multer.diskStorage({
  destination: (_, __, cb) => {
    const dir = "server/uploads";
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
    cb(null, dir);
  },
  filename: (_, file, cb) => cb(null, uuid() + path.extname(file.originalname))
});

const upload = multer({ 
  storage,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB per file
    files: 5 // max 5 files total
  },
  fileFilter: (req, file, cb) => {
    // Strict file type validation
    if (validateFileType(file)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'), false);
    }
  }
});

router.post(
  "/card",
  createRateLimit,
  validateRequestSize,
  upload.fields([
    { name: "photo", maxCount: 1 },
    { name: "logo", maxCount: 1 },
    { name: "products", maxCount: 3 }
  ]),
  async (req, res) => {
    try {
      // Sanitize inputs
      const sanitizedData = {};
      Object.keys(req.body).forEach(key => {
        sanitizedData[key] = sanitizeInput(req.body[key]);
      });

      // Validate required fields
      if (!sanitizedData.name || !sanitizedData.email) {
        return res.status(400).json({ error: "Name and email are required" });
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(sanitizedData.email)) {
        return res.status(400).json({ error: "Invalid email format" });
      }

      // Phone validation (if provided)
      if (sanitizedData.phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(sanitizedData.phone.replace(/[\s\-\(\)]/g, ''))) {
          return res.status(400).json({ error: "Invalid phone format" });
        }
      }

      const urls = {
        photo: req.files.photo ? `/uploads/${req.files.photo[0].filename}` : null,
        logo: req.files.logo ? `/uploads/${req.files.logo[0].filename}` : null,
        products: (req.files.products || []).map(
          (f) => `/uploads/${f.filename}`
        )
      };

      const cardData = {
        ...sanitizedData,
        ...urls,
        shuffle: sanitizedData.shuffle === 'true',
        seed: sanitizedData.seed || sanitizedData.email // Use email as default seed for consistency
      };

      const { pngUrl, pdfUrl, shareLink, qrCodeUrl, appleWalletUrl, googlePayUrl, cardId, style } = await generateCard(cardData);

      res.json({
        pngUrl,
        pdfUrl,
        shareLink,
        qrCodeUrl,
        appleWalletUrl,
        googlePayUrl,
        cardId,
        style,
        success: true
      });
      
    } catch (err) {
      console.error("Card generation error:", err);
      
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({ error: "File too large. Maximum 2MB per file." });
      }
      
      if (err.code === 'LIMIT_FILE_COUNT') {
        return res.status(400).json({ error: "Too many files uploaded." });
      }
      
      res.status(500).json({ 
        error: "Generation failed", 
        details: process.env.NODE_ENV === 'development' ? err.message : undefined 
      });
    }
  }
);

// Get card by ID for sharing
router.get("/card/:id", async (req, res) => {
  try {
    const cardId = req.params.id;
    // This would typically fetch from database
    // For now, redirect to S3 URL
    const pngUrl = `https://${process.env.S3_BUCKET}.s3.amazonaws.com/cards/${cardId}.png`;
    res.redirect(pngUrl);
  } catch (err) {
    console.error("Card retrieval error:", err);
    res.status(404).json({ error: "Card not found" });
  }
});

// Get wallet pass by ID
router.get("/wallet/:type/:id", async (req, res) => {
  try {
    const { type, id } = req.params;

    if (type === 'apple') {
      const passUrl = `https://${process.env.S3_BUCKET}.s3.amazonaws.com/passes/${id}.pkpass`;
      res.redirect(passUrl);
    } else if (type === 'google') {
      const passUrl = `https://${process.env.S3_BUCKET}.s3.amazonaws.com/passes/google-${id}.json`;
      res.redirect(passUrl);
    } else {
      res.status(400).json({ error: "Invalid wallet type. Use 'apple' or 'google'." });
    }
  } catch (err) {
    console.error("Wallet pass error:", err);
    res.status(500).json({ error: "Wallet pass retrieval failed" });
  }
});

// Shuffle style preview (without generating full card)
router.post("/shuffle", shuffleRateLimit, async (req, res) => {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({ error: "Email required for shuffle" });
    }

    // Generate new style with shuffle flag
    const { generateStyle } = await import("../utils/styleGenerator.js");
    const style = generateStyle(email, true); // true = shuffle

    res.json({
      style: {
        colors: style.colors,
        fonts: style.fonts,
        layout: style.layout,
        hash: style.hash
      }
    });
  } catch (err) {
    console.error("Shuffle error:", err);
    res.status(500).json({ error: "Shuffle failed" });
  }
});

// Local card generation (without S3) for testing
router.post("/card-local",
  createRateLimit,
  validateRequestSize,
  upload.fields([
    { name: "photo", maxCount: 1 },
    { name: "logo", maxCount: 1 },
    { name: "products", maxCount: 3 }
  ]),
  async (req, res) => {
    try {
      console.log('🧪 Local card generation started');

      // Sanitize inputs
      const sanitizedData = {};
      Object.keys(req.body).forEach(key => {
        sanitizedData[key] = sanitizeInput(req.body[key]);
      });

      // Validate required fields
      if (!sanitizedData.name || !sanitizedData.email) {
        return res.status(400).json({ error: "Name and email are required" });
      }

      const urls = {
        photo: req.files.photo ? `/uploads/${req.files.photo[0].filename}` : null,
        logo: req.files.logo ? `/uploads/${req.files.logo[0].filename}` : null,
        products: (req.files.products || []).map(
          (f) => `/uploads/${f.filename}`
        )
      };

      const cardData = {
        ...sanitizedData,
        ...urls,
        shuffle: sanitizedData.shuffle === 'true',
        seed: sanitizedData.seed || sanitizedData.email
      };

      // Use local generator
      const { default: generateCardLocal } = await import("../services/generatorLocal.js");
      const result = await generateCardLocal(cardData);

      console.log('✅ Local card generation successful');

      res.json({
        ...result,
        success: true,
        mode: 'local'
      });

    } catch (err) {
      console.error("Local card generation error:", err);
      res.status(500).json({
        error: "Local generation failed",
        details: process.env.NODE_ENV === 'development' ? err.message : undefined
      });
    }
  }
);

// Test endpoint for debugging
router.post("/test", async (req, res) => {
  try {
    console.log('🧪 Test endpoint called');
    console.log('Request body:', req.body);

    // Test basic functionality
    const testData = {
      name: 'Test User',
      email: '<EMAIL>',
      title: 'Test Title',
      company: 'Test Company'
    };

    // Test style generation
    const { generateStyle } = await import("../utils/styleGenerator.js");
    const style = generateStyle(testData.email);

    console.log('✅ Style generated:', style.colors.name);

    res.json({
      success: true,
      message: 'Test endpoint working',
      style: style.colors.name,
      environment: {
        nodeEnv: process.env.NODE_ENV,
        hasS3Config: !!(process.env.AWS_ACCESS_KEY && process.env.S3_BUCKET),
        serverBase: process.env.SERVER_BASE
      }
    });

  } catch (error) {
    console.error('❌ Test endpoint error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

export default router;
