import { useState } from "react";
import { useDropzone } from "react-dropzone";
import axios from "axios";

export default function Home() {
  const [fields, setFields] = useState({
    name: "",
    title: "",
    phone: "",
    email: "",
    website: ""
  });
  const [photo, setPhoto]   = useState(null);
  const [logo, setLogo]     = useState(null);
  const [products, setProds]= useState([]);

  const { getRootProps: gpPhoto, getInputProps: giPhoto } = useDropzone({
    accept: "image/*",
    maxFiles: 1,
    onDrop: (f) => setPhoto(f[0])
  });
  const { getRootProps: gpLogo, getInputProps: giLogo } = useDropzone({
    accept: "image/*",
    maxFiles: 1,
    onDrop: (f) => setLogo(f[0])
  });
  const { getRootProps: gpProds, getInputProps: giProds } = useDropzone({
    accept: "image/*",
    maxFiles: 3,
    onDrop: setProds
  });

  const handleChange = (e) =>
    setFields({ ...fields, [e.target.name]: e.target.value });

  const handleSubmit = async (e) => {
    e.preventDefault();
    const form = new FormData();
    Object.keys(fields).forEach((k) => form.append(k, fields[k]));
    if (photo)    form.append("photo", photo);
    if (logo)     form.append("logo", logo);
    products.forEach((f) => form.append("products", f));

    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_API_URL}/card`,
      form,
      { headers: { "Content-Type": "multipart/form-data" } }
    );
    window.open(data.pngUrl, "_blank");
  };

  return (
    <div className="container">
      <h1>Create your digital business card</h1>
      <form onSubmit={handleSubmit}>
        {["name", "title", "phone", "email", "website"].map((f) => (
          <input
            key={f}
            name={f}
            placeholder={f}
            value={fields[f]}
            onChange={handleChange}
          />
        ))}

        <div {...gpPhoto()} className="drop">
          <input {...giPhoto()} />
          {photo ? photo.name : "Upload photo"}
        </div>

        <div {...gpLogo()} className="drop">
          <input {...giLogo()} />
          {logo ? logo.name : "Upload logo"}
        </div>

        <div {...gpProds()} className="drop">
          <input {...giProds()} />
          {products.length ? `${products.length} images` : "Upload products (max 3)"}
        </div>

        <button type="submit">Generate & Download</button>
      </form>

      <style jsx>{`
        .container { max-width: 600px; margin: 50px auto; font-family: sans-serif; }
        input, button { width: 100%; margin: 8px 0; padding: 10px; }
        .drop { border: 2px dashed #ccc; padding: 20px; text-align: center; margin: 10px 0; cursor: pointer; }
      `}</style>
    </div>
  );
}