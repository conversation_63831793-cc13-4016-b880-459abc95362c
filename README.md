# 🎨 Digital Business Card Generator

A modern, full-stack application that generates unique, professional business cards in seconds. Every card is visually distinct with consistent styling based on user email, featuring 20 color palettes, 10 font combinations, and 3 layout templates.

## ✨ Features

### 🎯 Core Functionality
- **Unique Style Generation**: Hash-based styling ensures every user gets a consistent, unique design
- **Standard Size**: Perfect 3.5"×2" cards at 300 DPI (1050×600px)
- **Multiple Formats**: PNG for web, PDF for print
- **Instant Sharing**: Shareable links with QR codes
- **Wallet Integration**: Apple Wallet & Google Pay passes

### 🎨 Design System
- **20 Color Palettes**: From Ocean blues to Sunset oranges
- **10 Font Pairs**: Modern, Classic, Tech, Elegant, and more
- **3 Layout Templates**: Split, Header, and Sidebar layouts
- **Shuffle Feature**: Instantly regenerate styles while maintaining consistency

### 🔒 Security & Validation
- **File Validation**: 2MB per image, 10MB total request limit
- **Rate Limiting**: 10 cards per 15 minutes, 20 shuffles per minute
- **Input Sanitization**: XSS protection and data validation
- **Email/Phone Validation**: Proper format checking

### 📱 User Experience
- **Drag & Drop**: Easy file uploads with visual feedback
- **Real-time Preview**: Style preview before generation
- **Success Modal**: Beautiful results display with all download options
- **Responsive Design**: Works on desktop and mobile

## 🏗️ Architecture

```
├── client/          # Next.js React frontend
│   ├── pages/       # Next.js pages
│   ├── styles/      # Global CSS styles
│   └── package.json
├── server/          # Node.js Express backend
│   ├── routes/      # API endpoints
│   ├── services/    # Business logic
│   ├── utils/       # Utilities and helpers
│   ├── middleware/  # Security and validation
│   └── package.json
├── templates/       # Design templates (JSON)
└── README.md
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- AWS S3 bucket for file storage
- (Optional) Apple Developer account for Wallet passes

### 1. Clone and Install
```bash
git clone <repository-url>
cd digital-business-card
npm install
cd client && npm install
cd ../server && npm install
```

### 2. Environment Setup
```bash
# Copy environment files
cp server/.env.example server/.env
cp client/.env.local.example client/.env.local

# Edit server/.env with your AWS credentials
# Edit client/.env.local with your API URL
```

### 3. AWS S3 Setup
1. Create an S3 bucket (e.g., `your-business-cards-bucket`)
2. Set bucket policy for public read access on generated files
3. Add your AWS credentials to `server/.env`

### 4. Run Development
```bash
# From root directory
npm run dev

# Or run separately:
npm run dev:client  # http://localhost:3000
npm run dev:server  # http://localhost:5000
```

## 📋 Environment Variables

### Server (.env)
```env
PORT=5000
NODE_ENV=development
SERVER_BASE=http://localhost:5000
CLIENT_URL=http://localhost:3000

# AWS Configuration
AWS_ACCESS_KEY=your_access_key
AWS_SECRET_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET=your-bucket-name

# Optional: Wallet Integration
APPLE_PASS_TYPE_ID=pass.com.yourcompany.businesscard
APPLE_TEAM_ID=YOUR_TEAM_ID
GOOGLE_PAY_ISSUER_ID=your-issuer-id
```

### Client (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

## 🎨 Customization

### Adding New Color Palettes
Edit `server/utils/styleGenerator.js`:
```javascript
export const COLOR_PALETTES = [
  { name: "Custom", primary: "#your-color", secondary: "#your-color", accent: "#your-color", text: "#ffffff" },
  // ... existing palettes
];
```

### Adding New Templates
Create `templates/template-4.json`:
```json
{
  "name": "Your Template",
  "description": "Template description",
  "layout": {
    "type": "custom",
    "leftWidth": "40%",
    "rightWidth": "60%"
  }
}
```

## 🚀 Deployment

### Client (Vercel)
```bash
cd client
vercel --prod
```

### Server (Render/Railway/Heroku)
1. Connect your repository
2. Set environment variables
3. Deploy with build command: `cd server && npm install`
4. Start command: `cd server && npm start`

### Environment Variables for Production
- Update `SERVER_BASE` to your deployed server URL
- Update `CLIENT_URL` to your deployed client URL
- Set `NODE_ENV=production`
- Configure CORS origins properly

## 📊 API Endpoints

### POST /api/card
Generate a new business card
```javascript
// Form data with fields:
{
  name: "John Doe",
  title: "Software Engineer",
  company: "Tech Corp",
  phone: "+1234567890",
  email: "<EMAIL>",
  website: "https://johndoe.com",
  photo: File,
  logo: File,
  products: [File, File, File]
}

// Response:
{
  pngUrl: "https://...",
  pdfUrl: "https://...",
  shareLink: "https://...",
  qrCodeUrl: "https://...",
  appleWalletUrl: "https://...",
  googlePayUrl: "https://...",
  cardId: "unique-id",
  style: "Ocean"
}
```

### POST /api/shuffle
Preview new style without generating card
```javascript
// Request:
{ email: "<EMAIL>" }

// Response:
{
  style: {
    colors: { name: "Forest", primary: "#2d5016", ... },
    fonts: { name: "Modern", heading: "Segoe UI", ... },
    layout: { name: "Split", leftWidth: "35%", ... }
  }
}
```

### GET /api/card/:id
Retrieve shared card (redirects to S3)

### GET /api/wallet/:type/:id
Download wallet pass (apple/google)

## 🔧 Technical Details

### Style Generation Algorithm
1. Hash user email with MD5
2. Use hash segments to select:
   - Color palette (20 options)
   - Font pair (10 options)
   - Layout template (3 options)
3. Consistent results for same email
4. Shuffle adds timestamp to hash for variation

### Security Measures
- Rate limiting per IP
- File type validation
- Size limits (2MB per file, 10MB total)
- Input sanitization
- CORS configuration
- Security headers with Helmet

### Performance Optimizations
- Puppeteer headless rendering
- S3 CDN for file delivery
- Efficient image processing
- Minimal bundle sizes

## 🐛 Troubleshooting

### Common Issues

**Puppeteer fails to launch:**
```bash
# Install dependencies (Ubuntu/Debian)
sudo apt-get install -y libgbm-dev gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget
```

**S3 upload fails:**
- Check AWS credentials
- Verify bucket permissions
- Ensure bucket exists in correct region

**CORS errors:**
- Update `CLIENT_URL` in server environment
- Check CORS configuration in `server/index.js`

## 📄 License

MIT License - feel free to use this project for personal or commercial purposes.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 🎯 Roadmap

- [ ] Database integration for card storage
- [ ] User accounts and card management
- [ ] Advanced template editor
- [ ] Bulk card generation
- [ ] Analytics dashboard
- [ ] Custom domain support
- [ ] API rate limiting by user
- [ ] Advanced wallet pass features

---

**Made with ❤️ for creating beautiful business cards instantly**