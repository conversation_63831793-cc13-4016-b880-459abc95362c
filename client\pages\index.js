import { useState } from "react";
import { useDropzone } from "react-dropzone";
import axios from "axios";

export default function Home() {
  const [fields, setFields] = useState({
    name: "",
    title: "",
    company: "",
    phone: "",
    email: "",
    website: ""
  });
  const [photo, setPhoto] = useState(null);
  const [logo, setLogo] = useState(null);
  const [products, setProds] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentStyle, setCurrentStyle] = useState(null);
  const [isShuffling, setIsShuffling] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [generatedCard, setGeneratedCard] = useState(null);

  const { getRootProps: gpPhoto, getInputProps: giPhoto } = useDropzone({
    accept: { "image/*": [] },
    maxFiles: 1,
    maxSize: 2 * 1024 * 1024, // 2MB
    onDrop: (f) => setPhoto(f[0])
  });
  
  const { getRootProps: gpLogo, getInputProps: giLogo } = useDropzone({
    accept: { "image/*": [] },
    maxFiles: 1,
    maxSize: 2 * 1024 * 1024, // 2MB
    onDrop: (f) => setLogo(f[0])
  });
  
  const { getRootProps: gpProds, getInputProps: giProds } = useDropzone({
    accept: { "image/*": [] },
    maxFiles: 3,
    maxSize: 2 * 1024 * 1024, // 2MB per file
    onDrop: setProds
  });

  const handleChange = (e) =>
    setFields({ ...fields, [e.target.name]: e.target.value });

  const validateEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  const validatePhone = (phone) => {
    const re = /^[\+]?[1-9][\d]{0,15}$/;
    return re.test(phone.replace(/[\s\-\(\)]/g, ''));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!fields.name || !fields.email) {
      alert("Name and email are required");
      return;
    }
    
    if (!validateEmail(fields.email)) {
      alert("Please enter a valid email address");
      return;
    }
    
    if (fields.phone && !validatePhone(fields.phone)) {
      alert("Please enter a valid phone number");
      return;
    }

    setIsGenerating(true);
    
    try {
      const form = new FormData();
      Object.keys(fields).forEach((k) => form.append(k, fields[k]));
      if (photo) form.append("photo", photo);
      if (logo) form.append("logo", logo);
      products.forEach((f) => form.append("products", f));

      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/card`,
        form,
        { headers: { "Content-Type": "multipart/form-data" } }
      );
      
      // Open PNG in new tab
      window.open(data.pngUrl, "_blank");

      // Show success modal instead of alert
      showSuccessModal(data);
      
    } catch (error) {
      console.error("Generation failed:", error);
      alert("Failed to generate card. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const shuffleStyle = async () => {
    if (!fields.email) {
      alert("Please enter your email first to shuffle styles");
      return;
    }

    setIsShuffling(true);

    try {
      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/shuffle`,
        { email: fields.email }
      );

      setCurrentStyle(data.style);
      console.log("New style:", data.style);

    } catch (error) {
      console.error("Shuffle failed:", error);
      alert("Failed to shuffle style. Please try again.");
    } finally {
      setIsShuffling(false);
    }
  };

  const showSuccessModal = (data) => {
    setGeneratedCard(data);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setGeneratedCard(null);
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('Copied to clipboard!');
    });
  };

  return (
    <div className="container">
      <h1>Create Your Digital Business Card</h1>
      <p className="subtitle">Generate a unique, professional business card in seconds</p>
      
      <form onSubmit={handleSubmit}>
        {["name", "title", "company", "phone", "email", "website"].map((field) => (
          <div key={field} className="field-group">
            <label htmlFor={field}>{field.charAt(0).toUpperCase() + field.slice(1)}</label>
            <input
              id={field}
              name={field}
              type={field === "email" ? "email" : field === "phone" ? "tel" : "text"}
              placeholder={`Enter your ${field}`}
              value={fields[field]}
              onChange={handleChange}
              required={field === "name" || field === "email"}
            />
          </div>
        ))}

        <div className="upload-section">
          <div {...gpPhoto()} className="drop-zone">
            <input {...giPhoto()} />
            <div className="drop-content">
              {photo ? (
                <span>✓ {photo.name}</span>
              ) : (
                <>
                  <span className="drop-icon">📷</span>
                  <span>Upload Photo</span>
                  <small>Max 2MB</small>
                </>
              )}
            </div>
          </div>

          <div {...gpLogo()} className="drop-zone">
            <input {...giLogo()} />
            <div className="drop-content">
              {logo ? (
                <span>✓ {logo.name}</span>
              ) : (
                <>
                  <span className="drop-icon">🏢</span>
                  <span>Upload Logo</span>
                  <small>Max 2MB</small>
                </>
              )}
            </div>
          </div>

          <div {...gpProds()} className="drop-zone">
            <input {...giProds()} />
            <div className="drop-content">
              {products.length ? (
                <span>✓ {products.length} product image{products.length > 1 ? 's' : ''}</span>
              ) : (
                <>
                  <span className="drop-icon">📦</span>
                  <span>Upload Products</span>
                  <small>Max 3 images, 2MB each</small>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="button-group">
          <button
            type="button"
            onClick={shuffleStyle}
            disabled={isShuffling || !fields.email}
            className="shuffle-btn"
          >
            {isShuffling ? "Shuffling..." : "🎲 Shuffle Style"}
          </button>
          <button type="submit" disabled={isGenerating} className="generate-btn">
            {isGenerating ? "Generating..." : "Generate & Download"}
          </button>
        </div>

        {currentStyle && (
          <div className="style-preview">
            <h3>Current Style Preview</h3>
            <div className="style-info">
              <div className="color-palette">
                <span>Colors: {currentStyle.colors.name}</span>
                <div className="color-swatches">
                  <div
                    className="color-swatch"
                    style={{ backgroundColor: currentStyle.colors.primary }}
                    title="Primary"
                  ></div>
                  <div
                    className="color-swatch"
                    style={{ backgroundColor: currentStyle.colors.secondary }}
                    title="Secondary"
                  ></div>
                  <div
                    className="color-swatch"
                    style={{ backgroundColor: currentStyle.colors.accent }}
                    title="Accent"
                  ></div>
                </div>
              </div>
              <div className="font-info">
                <span>Font: {currentStyle.fonts.name}</span>
              </div>
              <div className="layout-info">
                <span>Layout: {currentStyle.layout.name}</span>
              </div>
            </div>
          </div>
        )}
      </form>

      {/* Success Modal */}
      {showModal && generatedCard && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>🎉 Card Generated Successfully!</h2>
              <button className="close-btn" onClick={closeModal}>×</button>
            </div>

            <div className="modal-body">
              <div className="card-preview">
                <img src={generatedCard.pngUrl} alt="Generated Business Card" />
              </div>

              <div className="download-section">
                <h3>📥 Downloads</h3>
                <div className="download-links">
                  <a href={generatedCard.pngUrl} target="_blank" className="download-btn png">
                    🖼️ PNG Image
                  </a>
                  <a href={generatedCard.pdfUrl} target="_blank" className="download-btn pdf">
                    📄 PDF Document
                  </a>
                </div>
              </div>

              <div className="share-section">
                <h3>🔗 Share & Save</h3>
                <div className="share-links">
                  <div className="share-item">
                    <span>Share Link:</span>
                    <div className="copy-group">
                      <input
                        type="text"
                        value={generatedCard.shareLink}
                        readOnly
                        className="copy-input"
                      />
                      <button
                        onClick={() => copyToClipboard(generatedCard.shareLink)}
                        className="copy-btn"
                      >
                        📋
                      </button>
                    </div>
                  </div>

                  <div className="wallet-options">
                    {generatedCard.appleWalletUrl && (
                      <a href={generatedCard.appleWalletUrl} className="wallet-btn apple">
                        📱 Add to Apple Wallet
                      </a>
                    )}
                    {generatedCard.googlePayUrl && (
                      <a href={generatedCard.googlePayUrl} className="wallet-btn google">
                        💳 Save to Google Pay
                      </a>
                    )}
                  </div>
                </div>
              </div>

              <div className="qr-section">
                <h3>📱 QR Code</h3>
                <img src={generatedCard.qrCodeUrl} alt="QR Code" className="qr-code" />
                <p>Scan to share your card instantly</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .container {
          max-width: 700px;
          margin: 50px auto;
          padding: 20px;
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          background: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
          text-align: center;
          color: #333;
          margin-bottom: 10px;
        }
        
        .subtitle {
          text-align: center;
          color: #666;
          margin-bottom: 30px;
        }
        
        .field-group {
          margin-bottom: 20px;
        }
        
        label {
          display: block;
          margin-bottom: 5px;
          font-weight: 600;
          color: #555;
        }
        
        input {
          width: 100%;
          padding: 12px;
          border: 2px solid #e1e5e9;
          border-radius: 8px;
          font-size: 16px;
          transition: border-color 0.3s;
        }
        
        input:focus {
          outline: none;
          border-color: #4f46e5;
        }
        
        .upload-section {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;
          margin: 30px 0;
        }
        
        .drop-zone {
          border: 2px dashed #d1d5db;
          border-radius: 8px;
          padding: 20px;
          text-align: center;
          cursor: pointer;
          transition: all 0.3s;
          background: #f9fafb;
        }
        
        .drop-zone:hover {
          border-color: #4f46e5;
          background: #f0f9ff;
        }
        
        .drop-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 5px;
        }
        
        .drop-icon {
          font-size: 24px;
        }
        
        small {
          color: #6b7280;
          font-size: 12px;
        }
        
        .button-group {
          display: flex;
          gap: 15px;
          margin-top: 30px;
        }
        
        .shuffle-btn {
          flex: 1;
          padding: 12px 24px;
          background: #f3f4f6;
          border: 2px solid #d1d5db;
          border-radius: 8px;
          font-size: 16px;
          cursor: pointer;
          transition: all 0.3s;
        }
        
        .shuffle-btn:hover {
          background: #e5e7eb;
          border-color: #9ca3af;
        }
        
        .generate-btn {
          flex: 2;
          padding: 12px 24px;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: background 0.3s;
        }
        
        .generate-btn:hover:not(:disabled) {
          background: #4338ca;
        }
        
        .generate-btn:disabled {
          background: #9ca3af;
          cursor: not-allowed;
        }

        .shuffle-btn:disabled {
          background: #f3f4f6;
          color: #9ca3af;
          cursor: not-allowed;
        }

        .style-preview {
          margin-top: 30px;
          padding: 20px;
          background: #f8fafc;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
        }

        .style-preview h3 {
          margin: 0 0 15px 0;
          color: #374151;
          font-size: 18px;
        }

        .style-info {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .color-palette {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .color-swatches {
          display: flex;
          gap: 5px;
        }

        .color-swatch {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          border: 1px solid #d1d5db;
          cursor: help;
        }

        .font-info, .layout-info {
          color: #6b7280;
          font-size: 14px;
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
          padding: 20px;
        }

        .modal-content {
          background: white;
          border-radius: 16px;
          max-width: 600px;
          width: 100%;
          max-height: 90vh;
          overflow-y: auto;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 24px 24px 0;
          border-bottom: 1px solid #e5e7eb;
          margin-bottom: 24px;
        }

        .modal-header h2 {
          margin: 0;
          color: #1f2937;
          font-size: 24px;
        }

        .close-btn {
          background: none;
          border: none;
          font-size: 32px;
          cursor: pointer;
          color: #6b7280;
          padding: 0;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.2s;
        }

        .close-btn:hover {
          background: #f3f4f6;
          color: #374151;
        }

        .modal-body {
          padding: 0 24px 24px;
        }

        .card-preview {
          text-align: center;
          margin-bottom: 24px;
        }

        .card-preview img {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .download-section, .share-section, .qr-section {
          margin-bottom: 24px;
        }

        .download-section h3, .share-section h3, .qr-section h3 {
          margin: 0 0 12px 0;
          color: #374151;
          font-size: 18px;
        }

        .download-links {
          display: flex;
          gap: 12px;
        }

        .download-btn {
          flex: 1;
          padding: 12px 16px;
          text-decoration: none;
          border-radius: 8px;
          text-align: center;
          font-weight: 600;
          transition: all 0.2s;
        }

        .download-btn.png {
          background: #3b82f6;
          color: white;
        }

        .download-btn.png:hover {
          background: #2563eb;
        }

        .download-btn.pdf {
          background: #ef4444;
          color: white;
        }

        .download-btn.pdf:hover {
          background: #dc2626;
        }

        .share-item {
          margin-bottom: 16px;
        }

        .share-item span {
          display: block;
          margin-bottom: 8px;
          font-weight: 600;
          color: #374151;
        }

        .copy-group {
          display: flex;
          gap: 8px;
        }

        .copy-input {
          flex: 1;
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
          background: #f9fafb;
        }

        .copy-btn {
          padding: 8px 12px;
          background: #6b7280;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          transition: background 0.2s;
        }

        .copy-btn:hover {
          background: #4b5563;
        }

        .wallet-options {
          display: flex;
          gap: 12px;
          margin-top: 12px;
        }

        .wallet-btn {
          flex: 1;
          padding: 10px 16px;
          text-decoration: none;
          border-radius: 8px;
          text-align: center;
          font-weight: 600;
          transition: all 0.2s;
        }

        .wallet-btn.apple {
          background: #000;
          color: white;
        }

        .wallet-btn.apple:hover {
          background: #333;
        }

        .wallet-btn.google {
          background: #4285f4;
          color: white;
        }

        .wallet-btn.google:hover {
          background: #3367d6;
        }

        .qr-section {
          text-align: center;
        }

        .qr-code {
          width: 150px;
          height: 150px;
          border-radius: 8px;
          margin: 12px 0;
        }

        .qr-section p {
          color: #6b7280;
          font-size: 14px;
          margin: 0;
        }
      `}</style>
    </div>
  );
}
