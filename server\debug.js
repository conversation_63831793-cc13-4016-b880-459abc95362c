import dotenv from 'dotenv';
import { generateStyle } from './utils/styleGenerator.js';
import { uploadToS3 } from './utils/s3.js';
import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

console.log('🔍 Digital Business Card Generator - Debug Script\n');

// Check environment variables
console.log('📋 Environment Variables:');
console.log('- NODE_ENV:', process.env.NODE_ENV || 'not set');
console.log('- PORT:', process.env.PORT || 'not set');
console.log('- SERVER_BASE:', process.env.SERVER_BASE || 'not set');
console.log('- AWS_ACCESS_KEY:', process.env.AWS_ACCESS_KEY ? '✅ set' : '❌ not set');
console.log('- AWS_SECRET_KEY:', process.env.AWS_SECRET_KEY ? '✅ set' : '❌ not set');
console.log('- AWS_REGION:', process.env.AWS_REGION || 'not set');
console.log('- S3_BUCKET:', process.env.S3_BUCKET || 'not set');
console.log('');

// Check directories
console.log('📁 Directory Structure:');
const uploadsDir = path.join(process.cwd(), 'uploads');
console.log('- Uploads directory exists:', fs.existsSync(uploadsDir) ? '✅' : '❌');
if (!fs.existsSync(uploadsDir)) {
  console.log('  Creating uploads directory...');
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('  ✅ Created uploads directory');
}

const tempDir = path.join(process.cwd(), 'temp');
console.log('- Temp directory exists:', fs.existsSync(tempDir) ? '✅' : '❌');
if (!fs.existsSync(tempDir)) {
  console.log('  Creating temp directory...');
  fs.mkdirSync(tempDir, { recursive: true });
  console.log('  ✅ Created temp directory');
}
console.log('');

// Test style generation
console.log('🎨 Testing Style Generation:');
try {
  const testStyle = generateStyle('<EMAIL>');
  console.log('✅ Style generation working');
  console.log('- Color palette:', testStyle.colors.name);
  console.log('- Font pair:', testStyle.fonts.name);
  console.log('- Layout:', testStyle.layout.name);
} catch (error) {
  console.log('❌ Style generation failed:', error.message);
}
console.log('');

// Test Puppeteer
console.log('🤖 Testing Puppeteer:');
try {
  const browser = await puppeteer.launch({ 
    headless: "new", 
    args: ["--no-sandbox", "--disable-setuid-sandbox"] 
  });
  console.log('✅ Puppeteer launched successfully');
  
  const page = await browser.newPage();
  await page.setContent('<html><body><h1>Test</h1></body></html>');
  console.log('✅ Page created and content set');
  
  const screenshot = await page.screenshot({ type: "png" });
  console.log('✅ Screenshot taken, size:', screenshot.length, 'bytes');
  
  await browser.close();
  console.log('✅ Browser closed');
} catch (error) {
  console.log('❌ Puppeteer test failed:', error.message);
  console.log('💡 Try installing system dependencies:');
  console.log('   sudo apt-get install -y libgbm-dev gconf-service libasound2 libatk1.0-0');
}
console.log('');

// Test S3 connection (if configured)
if (process.env.AWS_ACCESS_KEY && process.env.AWS_SECRET_KEY && process.env.S3_BUCKET) {
  console.log('☁️ Testing S3 Connection:');
  try {
    const testBuffer = Buffer.from('test file content');
    const testKey = `test/${Date.now()}.txt`;
    
    const url = await uploadToS3(testBuffer, testKey, 'text/plain');
    console.log('✅ S3 upload successful');
    console.log('- Test file URL:', url);
  } catch (error) {
    console.log('❌ S3 test failed:', error.message);
    console.log('💡 Check your AWS credentials and bucket permissions');
  }
} else {
  console.log('⚠️ S3 not configured - skipping S3 test');
  console.log('💡 Set AWS_ACCESS_KEY, AWS_SECRET_KEY, and S3_BUCKET in your .env file');
}
console.log('');

// Test card generation
console.log('🎴 Testing Card Generation:');
try {
  const testData = {
    name: 'John Doe',
    title: 'Software Engineer',
    company: 'Test Company',
    email: '<EMAIL>',
    phone: '+1234567890',
    website: 'https://johndoe.com',
    seed: '<EMAIL>'
  };

  // Import generator
  const { default: generateCard } = await import('./services/generator.js');
  
  if (process.env.AWS_ACCESS_KEY && process.env.AWS_SECRET_KEY && process.env.S3_BUCKET) {
    console.log('🚀 Attempting full card generation...');
    const result = await generateCard(testData);
    console.log('✅ Card generation successful!');
    console.log('- PNG URL:', result.pngUrl);
    console.log('- PDF URL:', result.pdfUrl);
    console.log('- Share Link:', result.shareLink);
  } else {
    console.log('⚠️ Skipping full card generation (S3 not configured)');
    console.log('💡 Configure S3 to test full card generation');
  }
} catch (error) {
  console.log('❌ Card generation failed:', error.message);
  console.log('Stack trace:', error.stack);
}

console.log('\n🏁 Debug complete!');
console.log('💡 If you see any ❌ errors above, fix those first.');
console.log('💡 For more help, check the README.md troubleshooting section.');

process.exit(0);
