{"name": "server", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js"}, "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "uuid": "^9.0.1", "puppeteer": "^21.4.0", "@aws-sdk/client-s3": "^3.445.0", "dotenv": "^16.3.1", "qrcode": "^1.5.3", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "archiver": "^6.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}