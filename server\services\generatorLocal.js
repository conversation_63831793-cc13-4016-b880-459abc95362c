import puppeteer from "puppeteer";
import path from "path";
import fs from "fs";
import QRCode from "qrcode";
import { generateStyle } from "../utils/styleGenerator.js";

// Local version that saves files to disk instead of S3
export default async function generateCardLocal(data) {
  const browser = await puppeteer.launch({ 
    headless: "new", 
    args: ["--no-sandbox", "--disable-setuid-sandbox"] 
  });
  
  try {
    const page = await browser.newPage();

    // Generate unique style based on seed
    const style = generateStyle(data.seed, data.shuffle);
    
    // Build HTML with generated style
    const html = buildHTML(data, style);
    await page.setContent(html, { waitUntil: "networkidle0" });

    // Generate unique ID for this card
    const cardId = Date.now().toString() + '-' + style.hash;

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Screenshot PNG (1050x600 for 3.5"x2" at 300 DPI)
    const pngBuffer = await page.screenshot({ 
      type: "png", 
      clip: { x: 0, y: 0, width: 1050, height: 600 },
      omitBackground: false
    });
    
    const pngPath = path.join(uploadsDir, `${cardId}.png`);
    fs.writeFileSync(pngPath, pngBuffer);
    const pngUrl = `${process.env.SERVER_BASE || 'http://localhost:5000'}/uploads/${cardId}.png`;

    // Generate PDF
    await page.addStyleTag({ content: "body { zoom: 1.0 }" });
    const pdfBuffer = await page.pdf({ 
      width: "3.5in", 
      height: "2in", 
      printBackground: true,
      margin: { top: 0, right: 0, bottom: 0, left: 0 }
    });
    
    const pdfPath = path.join(uploadsDir, `${cardId}.pdf`);
    fs.writeFileSync(pdfPath, pdfBuffer);
    const pdfUrl = `${process.env.SERVER_BASE || 'http://localhost:5000'}/uploads/${cardId}.pdf`;

    // Generate QR code for sharing
    const shareLink = `${process.env.SHARE_BASE_URL || 'http://localhost:3000'}/card/${cardId}`;
    const qrCodeBuffer = await QRCode.toBuffer(shareLink, {
      width: 200,
      margin: 2,
      color: {
        dark: style.colors.primary,
        light: '#FFFFFF'
      }
    });
    
    const qrPath = path.join(uploadsDir, `${cardId}-qr.png`);
    fs.writeFileSync(qrPath, qrCodeBuffer);
    const qrCodeUrl = `${process.env.SERVER_BASE || 'http://localhost:5000'}/uploads/${cardId}-qr.png`;

    return { 
      pngUrl, 
      pdfUrl, 
      shareLink,
      qrCodeUrl,
      appleWalletUrl: null, // Not available in local mode
      googlePayUrl: null,   // Not available in local mode
      cardId,
      style: style.colors.name
    };
    
  } finally {
    await browser.close();
  }
}

function buildHTML(data, style) {
  const { colors, fonts, layout } = style;
  const { name, title, company, phone, email, website, photo, logo, products } = data;

  const serverBase = process.env.SERVER_BASE || "http://localhost:5000";
  const img = (src) => src ? `<img src="${serverBase}${src}" />` : "";

  const productsHTML = (products || [])
    .slice(0, 3)
    .map((p) => `<div class="prod">${img(p)}</div>`)
    .join("");

  // Build contact info
  const contactInfo = [
    phone && `<p class="contact-item">📱 ${phone}</p>`,
    email && `<p class="contact-item">✉️ ${email}</p>`,
    website && `<p class="contact-item">🌐 ${website}</p>`
  ].filter(Boolean).join('');

  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&family=Open+Sans:wght@400;600&display=swap');
    
    body { 
      margin: 0; 
      width: 1050px; 
      height: 600px; 
      font-family: ${fonts.body}; 
      display: flex; 
      align-items: center; 
      justify-content: center; 
      background: #fafafa; 
      overflow: hidden;
    }
    
    .card { 
      width: 1050px; 
      height: 600px; 
      border: 1px solid #ddd; 
      display: flex; 
      background: white; 
      position: relative;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .left { 
      width: ${layout.leftWidth}; 
      background: linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%); 
      display: flex; 
      flex-direction: column; 
      align-items: center; 
      justify-content: center; 
      color: ${colors.text}; 
      padding: 40px 30px;
      position: relative;
    }
    
    .left::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255,255,255,0.1);
      backdrop-filter: blur(10px);
    }
    
    .left > * {
      position: relative;
      z-index: 1;
    }
    
    .photo-container {
      width: 180px;
      height: 180px;
      border-radius: 50%;
      overflow: hidden;
      margin-bottom: 20px;
      border: 4px solid rgba(255,255,255,0.3);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }
    
    .photo-container img { 
      width: 100%; 
      height: 100%; 
      object-fit: cover; 
    }
    
    .right { 
      width: ${layout.rightWidth}; 
      padding: 60px; 
      display: flex; 
      flex-direction: column; 
      justify-content: center;
      position: relative;
    }
    
    .logo { 
      position: absolute; 
      top: 30px; 
      right: 30px; 
      width: 120px; 
      height: 120px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .logo img { 
      width: 100%; 
      height: 100%; 
      object-fit: contain; 
      background: white;
      padding: 10px;
    }
    
    h1 { 
      margin: 0; 
      font-size: 52px; 
      font-family: ${fonts.heading};
      font-weight: 700;
      color: ${colors.primary};
      line-height: 1.1;
    }
    
    h2 { 
      margin: 5px 0 10px; 
      font-weight: 600; 
      color: ${colors.secondary};
      font-size: 32px;
      font-family: ${fonts.heading};
    }
    
    .company {
      margin: 0 0 25px;
      font-size: 24px;
      color: #666;
      font-style: italic;
    }
    
    .contact-item { 
      margin: 8px 0; 
      font-size: 28px; 
      color: #333;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .prods { 
      display: flex; 
      margin-top: 30px; 
      gap: 15px;
      flex-wrap: wrap;
    }
    
    .prod {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .prod img { 
      width: 100px; 
      height: 80px; 
      object-fit: cover; 
    }
    
    .accent-bar {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 8px;
      background: linear-gradient(90deg, ${colors.accent} 0%, ${colors.secondary} 100%);
    }
  </style>
</head>
<body>
  <div class="card">
    <div class="left">
      ${photo ? `<div class="photo-container">${img(photo)}</div>` : ''}
    </div>
    <div class="right">
      ${logo ? `<div class="logo">${img(logo)}</div>` : ''}
      <h1>${name || 'Your Name'}</h1>
      <h2>${title || 'Your Title'}</h2>
      ${company ? `<p class="company">${company}</p>` : ''}
      <div class="contact-info">
        ${contactInfo}
      </div>
      ${productsHTML ? `<div class="prods">${productsHTML}</div>` : ''}
    </div>
    <div class="accent-bar"></div>
  </div>
</body>
</html>`;
}
